"""
Cleanup Processor for Amazon Affiliate Integration

Handles removal of failed (visible) shortcodes from WordPress articles
while preserving Gutenberg block structure.
"""

import re
import asyncio
import logging
from typing import List, Dict, Optional, Set
from datetime import datetime

from ..core.models import (
    CleanupResult, CleanupAction, CrawlerResult, VisibleShortcode, ArticleInfo
)
from ..clients.wordpress_client import WordPressClient
from ..processors.state_manager import StateManager


class CleanupProcessor:
    """
    Processes cleanup of failed shortcodes from WordPress articles
    
    Features:
    - Removes visible shortcodes while preserving content structure
    - Handles Gutenberg block format properly
    - Async processing with concurrency control
    - Comprehensive logging and error handling
    - State tracking for cleanup actions
    """
    
    # Pattern to match shortcode blocks (including heading) - handles both ASCII and Unicode quotes
    SHORTCODE_BLOCK_PATTERN = re.compile(
        r'<!-- wp:heading -->\s*'
        r'<h3[^>]*class="wp-block-heading"[^>]*>[^<]*</h3>\s*'
        r'<!-- /wp:heading -->\s*'
        r'<!-- wp:shortcode -->\s*'
        r'\[amazon bestseller=[\"\u201d\u2033]([^\""\u201d\u2033]+)[\"\u201d\u2033][^\]]*\]\s*'
        r'<!-- /wp:shortcode -->',
        re.IGNORECASE | re.MULTILINE | re.DOTALL
    )

    # Pattern to match just the shortcode part - handles both ASCII and Unicode quotes
    SHORTCODE_ONLY_PATTERN = re.compile(
        r'<!-- wp:shortcode -->\s*'
        r'\[amazon bestseller=[\"\u201d\u2033]([^\""\u201d\u2033]+)[\"\u201d\u2033][^\]]*\]\s*'
        r'<!-- /wp:shortcode -->',
        re.IGNORECASE | re.MULTILINE | re.DOTALL
    )

    # Pattern to match shortcodes directly embedded in HTML (not in Gutenberg blocks)
    # Handles HTML entities (&#8221; for quotes)
    DIRECT_SHORTCODE_PATTERN = re.compile(
        r'\[amazon bestseller=&#8221;([^&]+?)&#8221;[^\]]*\]',
        re.IGNORECASE | re.MULTILINE
    )
    
    def __init__(self, wordpress_client: WordPressClient, state_manager: StateManager, 
                 concurrency_limit: int = 4):
        """
        Initialize CleanupProcessor
        
        Args:
            wordpress_client: WordPress API client
            state_manager: State management instance
            concurrency_limit: Max concurrent cleanup operations
        """
        self.wordpress_client = wordpress_client
        self.state_manager = state_manager
        self.concurrency_limit = concurrency_limit
        self.logger = logging.getLogger(__name__)
        
        self.semaphore = asyncio.Semaphore(concurrency_limit)
        
        # Statistics
        self.stats = {
            'articles_processed': 0,
            'shortcodes_removed': 0,
            'cleanup_failures': 0,
            'articles_updated': 0
        }
    
    async def cleanup_failed_shortcodes(self, crawler_results: List[CrawlerResult]) -> List[CleanupResult]:
        """
        Clean up failed shortcodes based on crawler results
        
        Args:
            crawler_results: Results from shortcode crawler
            
        Returns:
            List of cleanup results
        """
        # Filter results that have visible shortcodes
        articles_to_clean = [
            result for result in crawler_results 
            if result.success and result.has_visible_shortcodes
        ]
        
        if not articles_to_clean:
            self.logger.info("No articles with visible shortcodes found - no cleanup needed")
            return []
        
        self.logger.info(f"Starting cleanup for {len(articles_to_clean)} articles with visible shortcodes")
        
        # Reset statistics
        self.stats = {key: 0 for key in self.stats}
        
        # Create cleanup tasks
        tasks = [
            self._cleanup_single_article(result)
            for result in articles_to_clean
        ]
        
        # Execute with progress reporting
        results = []
        completed = 0
        
        for coro in asyncio.as_completed(tasks):
            try:
                result = await coro
                results.append(result)
                completed += 1
                
                # Update statistics
                self.stats['articles_processed'] += 1
                if result.success:
                    self.stats['articles_updated'] += 1
                    self.stats['shortcodes_removed'] += len(result.actions_performed)
                else:
                    self.stats['cleanup_failures'] += 1
                
                # Progress reporting
                if completed % 5 == 0 or completed == len(articles_to_clean):
                    self.logger.info(
                        f"Cleaned up {completed}/{len(articles_to_clean)} articles "
                        f"({self.stats['shortcodes_removed']} shortcodes removed)"
                    )
                    
            except Exception as e:
                self.logger.error(f"Unexpected error in cleanup task: {e}")
                # Create failed result
                failed_result = CleanupResult(
                    url="unknown",
                    domain="unknown",
                    success=False
                )
                results.append(failed_result)
        
        self.logger.info(f"Cleanup completed: {self.stats}")
        return results
    
    async def _cleanup_single_article(self, crawler_result: CrawlerResult) -> CleanupResult:
        """
        Clean up visible shortcodes from a single article
        
        Args:
            crawler_result: Crawler result with visible shortcodes
            
        Returns:
            Cleanup result
        """
        async with self.semaphore:
            try:
                # Get article information from WordPress
                article = await self.wordpress_client.get_article_by_url(crawler_result.url)
                
                if not article:
                    return CleanupResult(
                        url=crawler_result.url,
                        domain=crawler_result.domain,
                        success=False
                    )
                
                # Create backup before cleanup
                backup_created = await self._create_cleanup_backup(article)
                if not backup_created:
                    self.logger.warning(f"Failed to create backup for {crawler_result.url}")
                
                # Debug: Log article content and visible shortcodes
                self.logger.debug(f"Article content length: {len(article.content)}")
                self.logger.debug(f"Visible shortcodes to remove: {len(crawler_result.visible_shortcodes)}")
                for i, shortcode in enumerate(crawler_result.visible_shortcodes):
                    self.logger.debug(f"  {i+1}. Product: '{shortcode.product_name}' | Full: '{shortcode.full_shortcode}'")

                # Debug: Check if content contains any Amazon shortcodes at all
                amazon_count = article.content.count('[amazon')
                self.logger.debug(f"WordPress content contains {amazon_count} '[amazon' strings")

                # Debug: Show a snippet of the content to understand its structure
                content_snippet = article.content[:2000] if len(article.content) > 2000 else article.content
                self.logger.debug(f"Content snippet: {content_snippet}")

                # Process content cleanup
                cleaned_content, actions = self._remove_visible_shortcodes(
                    article.content,
                    crawler_result.visible_shortcodes
                )
                
                if not actions:
                    self.logger.info(f"No shortcodes to remove from {crawler_result.url}")
                    return CleanupResult(
                        url=crawler_result.url,
                        domain=crawler_result.domain,
                        success=True
                    )
                
                # Update article in WordPress
                update_success = await self.wordpress_client.update_article_content(
                    article.id, cleaned_content
                )
                
                if update_success:
                    # Update state - remove missing products for this URL
                    await self.state_manager.remove_missing_products(crawler_result.url)
                    
                    self.logger.info(
                        f"Successfully cleaned up {len(actions)} shortcodes from {crawler_result.url}"
                    )
                    
                    return CleanupResult(
                        url=crawler_result.url,
                        domain=crawler_result.domain,
                        actions_performed=actions,
                        success=True
                    )
                else:
                    self.logger.error(f"Failed to update article content for {crawler_result.url}")
                    return CleanupResult(
                        url=crawler_result.url,
                        domain=crawler_result.domain,
                        success=False
                    )
                    
            except Exception as e:
                self.logger.error(f"Error cleaning up {crawler_result.url}: {e}")
                return CleanupResult(
                    url=crawler_result.url,
                    domain=crawler_result.domain,
                    success=False
                )
    
    def _remove_visible_shortcodes(self, content: str, visible_shortcodes: List[VisibleShortcode]) -> tuple:
        """
        Remove visible shortcodes from article content
        
        Args:
            content: Original article content
            visible_shortcodes: List of shortcodes to remove
            
        Returns:
            Tuple of (cleaned_content, list_of_actions)
        """
        cleaned_content = content
        actions = []

        self.logger.debug(f"Starting shortcode removal from content ({len(content)} chars)")
        self.logger.debug(f"Looking for {len(visible_shortcodes)} visible shortcodes")

        # Sort shortcodes by position (descending) to avoid position shifts
        sorted_shortcodes = sorted(
            visible_shortcodes,
            key=lambda x: x.position_in_content,
            reverse=True
        )
        
        for shortcode in sorted_shortcodes:
            self.logger.debug(f"Processing shortcode for product: '{shortcode.product_name}'")

            # Try to remove the full block (heading + shortcode)
            block_removed = False

            # Look for the full block pattern around this shortcode
            block_matches = list(self.SHORTCODE_BLOCK_PATTERN.finditer(cleaned_content))
            self.logger.debug(f"Found {len(block_matches)} shortcode blocks in content")

            for match in block_matches:
                if shortcode.product_name in match.group(0):
                    self.logger.debug(f"Found matching block for product '{shortcode.product_name}'")
                    # Remove the entire block
                    cleaned_content = (
                        cleaned_content[:match.start()] + 
                        cleaned_content[match.end():]
                    )
                    block_removed = True
                    
                    action = CleanupAction(
                        url="",  # Will be set by caller
                        article_id=0,  # Will be set by caller
                        shortcode_to_remove=shortcode.full_shortcode,
                        heading=shortcode.heading,
                        product_name=shortcode.product_name
                    )
                    actions.append(action)
                    
                    self.logger.debug(
                        f"Removed full shortcode block for product '{shortcode.product_name}'"
                    )
                    break
            
            # If full block removal failed, try to remove just the shortcode
            if not block_removed:
                self.logger.debug(f"Block removal failed for '{shortcode.product_name}', trying shortcode-only pattern")
                shortcode_matches = list(self.SHORTCODE_ONLY_PATTERN.finditer(cleaned_content))
                self.logger.debug(f"Found {len(shortcode_matches)} shortcode-only matches")

                for match in shortcode_matches:
                    if match.group(1) == shortcode.product_name:
                        self.logger.debug(f"Found matching shortcode for product '{shortcode.product_name}'")
                        # Remove just the shortcode part
                        cleaned_content = (
                            cleaned_content[:match.start()] + 
                            cleaned_content[match.end():]
                        )
                        
                        action = CleanupAction(
                            url="",  # Will be set by caller
                            article_id=0,  # Will be set by caller
                            shortcode_to_remove=shortcode.full_shortcode,
                            heading=shortcode.heading,
                            product_name=shortcode.product_name
                        )
                        actions.append(action)
                        
                        self.logger.debug(
                            f"Removed shortcode only for product '{shortcode.product_name}'"
                        )
                        break

                # If both block and shortcode-only removal failed, try direct shortcode pattern
                if not block_removed:
                    self.logger.debug(f"Shortcode-only removal failed for '{shortcode.product_name}', trying direct shortcode pattern")

                    # Create a pattern to match the specific product's shortcode
                    product_pattern = re.compile(
                        rf'\[amazon bestseller=&#8221;{re.escape(shortcode.product_name)}&#8221;[^\]]*\]',
                        re.IGNORECASE | re.MULTILINE
                    )

                    product_matches = list(product_pattern.finditer(cleaned_content))
                    self.logger.debug(f"Found {len(product_matches)} direct shortcode matches for '{shortcode.product_name}'")

                    if product_matches:
                        # Remove the first match
                        match = product_matches[0]
                        self.logger.debug(f"Found matching direct shortcode for product '{shortcode.product_name}' at position {match.start()}-{match.end()}")

                        # Remove the direct shortcode
                        cleaned_content = (
                            cleaned_content[:match.start()] +
                            cleaned_content[match.end():]
                        )

                        action = CleanupAction(
                            url="",  # Will be set by caller
                            article_id=0,  # Will be set by caller
                            shortcode_to_remove=match.group(0),  # Use the actual matched shortcode
                            heading=shortcode.heading,
                            product_name=shortcode.product_name
                        )
                        actions.append(action)

                        self.logger.debug(
                            f"Removed direct shortcode for product '{shortcode.product_name}'"
                        )
                        block_removed = True

        return cleaned_content, actions
    
    async def _create_cleanup_backup(self, article: ArticleInfo) -> bool:
        """
        Create backup before cleanup operations
        
        Args:
            article: Article to backup
            
        Returns:
            True if backup was successful
        """
        try:
            backup_filename = f"cleanup_backup_{article.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            backup_data = {
                'article_id': article.id,
                'url': article.url,
                'title': article.title,
                'content': article.content,
                'backup_created_at': datetime.now().isoformat(),
                'backup_type': 'cleanup'
            }
            
            # This would typically save to a backup directory
            # For now, we'll log the backup creation
            self.logger.debug(f"Created cleanup backup: {backup_filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create cleanup backup for article {article.id}: {e}")
            return False
    
    def get_statistics(self) -> Dict:
        """Get cleanup statistics"""
        return self.stats.copy()
